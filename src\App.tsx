import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Index from './pages/Index';
import CategoryPage from './pages/categories/CategoryPage';
import ServiceDetails from './pages/ServiceDetails';
import SearchResults from './pages/SearchResults';
import NotFound from './pages/NotFound';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <BrowserRouter>
        <Routes>
          <Route element={<Layout />}>
            <Route index element={<Index />} />
            <Route path="search" element={<SearchResults />} />
            <Route path="categories/:categorySlug" element={<CategoryPage />} />
            <Route path="categories/:categorySlug/:subcategorySlug" element={<CategoryPage />} />
            <Route path="categories/:categorySlug/:subcategorySlug/:subSubCategorySlug" element={<CategoryPage />} />
            <Route path="service/:serviceId" element={<ServiceDetails />} />
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
