import { Link, useNavigate } from 'react-router-dom';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { categoriesData } from '@/data/categories';
import { useState } from 'react';

export default function HeroSection() {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const searchParams = new URLSearchParams();
      searchParams.set('q', searchQuery.trim());
      navigate(`/search?${searchParams.toString()}`);
    }
  };
  return (
    <section className="relative py-16 md:py-24 overflow-hidden">
      {/* Background with overlay */}
      <div className="absolute inset-0 bg-morocco-navy/10 backdrop-blur-sm">
        <div
          className="absolute inset-0 bg-cover bg-center opacity-40"
          style={{ backgroundImage: "url('/assets/hero-background.jpg')" }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/80"></div>
      </div>
      
      <div className="relative container mx-auto px-4 text-center">
        <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-4 md:mb-6 max-w-3xl mx-auto">
          Find the Perfect Services for Your 
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-morocco-teal to-morocco-gold ml-2">
            Moroccan Celebration
          </span>
        </h1>
        
        <p className="text-lg md:text-xl text-foreground/90 max-w-2xl mx-auto mb-8 md:mb-10 leading-relaxed">
          Discover the finest providers for all your celebration needs, from weddings and engagements to traditional ceremonies
        </p>
        
        {/* Search bar */}
        <div className="max-w-2xl mx-auto mb-10 md:mb-12">
          <form className="relative" onSubmit={handleSearch}>
            <Input
              type="search"
              placeholder="Search for services, venues, photographers..."
              className="w-full h-12 pl-12 pr-4 rounded-full border border-border bg-background/90 backdrop-blur-sm shadow-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Button
              type="submit"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 rounded-full bg-morocco-teal hover:bg-morocco-teal/90"
            >
              Search
            </Button>
          </form>
        </div>
        
        {/* Category quicklinks */}
        <div className="flex flex-wrap justify-center gap-3 max-w-3xl mx-auto">
          {categoriesData.slice(0, 6).map((category) => (
            <Link to={`/categories/${category.slug}`} key={category.id}>
              <Button 
                variant="outline" 
                size="sm" 
                className="rounded-full border-border/50 backdrop-blur-sm bg-background/50 hover:bg-morocco-teal hover:text-white hover:border-transparent transition-colors"
              >
                {category.name}
              </Button>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}