import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { 
  ChevronRight, 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Instagram, 
  Facebook, 
  Share2 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { getProviderById } from '@/data/providers';
import { getCategoryBySlug, getSubcategoryBySlug } from '@/data/categories';
import { getLocationById } from '@/data/locations';
import { ServiceProvider } from '@/types';

export default function ServiceDetails() {
  const { serviceId } = useParams<{ serviceId: string }>();
  const [provider, setProvider] = useState<ServiceProvider | null>(null);
  const [activeImage, setActiveImage] = useState<string>('');
  const [imageError, setImageError] = useState(false);
  
  useEffect(() => {
    if (serviceId) {
      const serviceData = getProviderById(serviceId);
      if (serviceData) {
        setProvider(serviceData);
        if (serviceData.images && serviceData.images.length > 0) {
          setActiveImage(serviceData.images[0]);
        }
      }
    }
  }, [serviceId]);
  
  if (!provider) {
    return (
      <div className="container mx-auto px-4 py-12">
        <p>Service not found.</p>
      </div>
    );
  }
  
  const category = getCategoryBySlug(provider.category);
  const subcategory = getSubcategoryBySlug(provider.category, provider.subcategory);
  const location = getLocationById(provider.location);
  
  return (
    <div>
      {/* Breadcrumbs */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <Link to="/" className="hover:text-morocco-teal">Home</Link>
          <ChevronRight className="h-4 w-4 mx-1" />
          <Link to={`/categories/${provider.category}`} className="hover:text-morocco-teal">
            {category?.name || provider.category}
          </Link>
          <ChevronRight className="h-4 w-4 mx-1" />
          <span>{provider.name}</span>
        </div>
      </div>
      
      {/* Hero section */}
      <div className="bg-muted/30 py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between">
            <div>
              <h1 className="font-serif text-3xl md:text-4xl font-bold mb-2">
                {provider.name}
              </h1>
              
              <div className="flex items-center gap-4 mb-4">
                {provider.rating && (
                  <div className="flex items-center">
                    <Star className="h-4 w-4 fill-morocco-gold text-morocco-gold" />
                    <span className="ml-1 font-medium">{provider.rating}</span>
                  </div>
                )}
                
                {provider.priceRange && (
                  <div className="px-2 py-1 bg-morocco-gold/10 rounded text-sm font-medium">
                    {provider.priceRange}
                  </div>
                )}
                
                {location && (
                  <div className="flex items-center text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{location.name}</span>
                  </div>
                )}
              </div>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {category && (
                  <Link 
                    to={`/categories/${category.slug}`}
                    className="text-sm px-3 py-1 bg-morocco-teal/10 text-morocco-teal rounded-full hover:bg-morocco-teal/20"
                  >
                    {category.name}
                  </Link>
                )}
                
                {subcategory && (
                  <Link 
                    to={`/categories/${provider.category}/${provider.subcategory}`}
                    className="text-sm px-3 py-1 bg-muted rounded-full hover:bg-muted/80"
                  >
                    {subcategory.name}
                  </Link>
                )}
              </div>
            </div>
            
            <div className="mt-4 md:mt-0 flex gap-2">
              <Button className="bg-morocco-teal hover:bg-morocco-teal/90">
                Contact
              </Button>
              <Button variant="outline" className="border-morocco-teal text-morocco-teal">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main content */}
          <div className="lg:col-span-2">
            {/* Image gallery */}
            <div className="mb-8">
              <div className="h-[400px] md:h-[500px] rounded-lg overflow-hidden mb-2">
                {activeImage && !imageError ? (
                  <img
                    src={activeImage}
                    alt={provider.name}
                    className="w-full h-full object-cover"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-full bg-muted flex items-center justify-center">
                    <span className="text-muted-foreground">
                      {imageError ? 'Image not available' : 'No image available'}
                    </span>
                  </div>
                )}
              </div>
              
              {provider.images && provider.images.length > 1 && (
                <div className="flex gap-2 overflow-x-auto pb-2">
                  {provider.images.map((image, index) => (
                    <div 
                      key={index}
                      className={`h-20 w-20 rounded-md overflow-hidden cursor-pointer border-2 ${
                        image === activeImage ? 'border-morocco-teal' : 'border-transparent'
                      }`}
                      onClick={() => setActiveImage(image)}
                    >
                      <img
                        src={image}
                        alt={`${provider.name} - ${index + 1}`}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Tabs for information */}
            <Tabs defaultValue="about">
              <TabsList className="w-full mb-6">
                <TabsTrigger value="about" className="flex-1">About</TabsTrigger>
                <TabsTrigger value="services" className="flex-1">Services</TabsTrigger>
                <TabsTrigger value="reviews" className="flex-1">Reviews</TabsTrigger>
              </TabsList>
              
              <TabsContent value="about" className="pt-4">
                <h3 className="font-serif text-xl font-semibold mb-3">About {provider.name}</h3>
                <p className="mb-6">{provider.description}</p>
                
                <h4 className="font-medium mb-2">Details</h4>
                <ul className="space-y-2 mb-6">
                  {category && <li><span className="text-muted-foreground">Category:</span> {category.name}</li>}
                  {subcategory && <li><span className="text-muted-foreground">Type:</span> {subcategory.name}</li>}
                  {location && <li><span className="text-muted-foreground">Location:</span> {location.name}</li>}
                  {provider.priceRange && <li><span className="text-muted-foreground">Price Range:</span> {provider.priceRange}</li>}
                </ul>
              </TabsContent>
              
              <TabsContent value="services" className="pt-4">
                <h3 className="font-serif text-xl font-semibold mb-3">Services Offered</h3>
                <p className="text-muted-foreground mb-4">
                  This content would be customized for each provider. For the demonstration,
                  we're showing a placeholder.
                </p>
                
                <div className="space-y-4">
                  <div className="p-4 border border-border rounded-md">
                    <h4 className="font-medium">Premium Package</h4>
                    <p className="text-sm text-muted-foreground">Complete service with all features</p>
                  </div>
                  
                  <div className="p-4 border border-border rounded-md">
                    <h4 className="font-medium">Standard Package</h4>
                    <p className="text-sm text-muted-foreground">Essential services for your needs</p>
                  </div>
                  
                  <div className="p-4 border border-border rounded-md">
                    <h4 className="font-medium">Basic Package</h4>
                    <p className="text-sm text-muted-foreground">Entry-level option with core features</p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="reviews" className="pt-4">
                <h3 className="font-serif text-xl font-semibold mb-3">Client Reviews</h3>
                <p className="text-muted-foreground mb-4">
                  Reviews would be displayed here. For the demonstration, 
                  we're showing placeholder content.
                </p>
                
                <div className="space-y-6">
                  {/* Sample reviews */}
                  <div className="p-4 border border-border rounded-md">
                    <div className="flex items-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`h-4 w-4 ${i < 5 ? 'text-morocco-gold fill-morocco-gold' : 'text-muted'}`} 
                        />
                      ))}
                    </div>
                    <p className="mb-2">"Excellent service! They made our wedding day perfect. Highly recommended for anyone planning a celebration."</p>
                    <div className="text-sm text-muted-foreground">Amina B. - Wedding</div>
                  </div>
                  
                  <div className="p-4 border border-border rounded-md">
                    <div className="flex items-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`h-4 w-4 ${i < 4 ? 'text-morocco-gold fill-morocco-gold' : 'text-muted'}`} 
                        />
                      ))}
                    </div>
                    <p className="mb-2">"Great quality and professional team. They were responsive and delivered as promised."</p>
                    <div className="text-sm text-muted-foreground">Karim H. - Engagement Party</div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
          
          {/* Sidebar */}
          <div>
            <div className="sticky top-24 bg-muted/30 rounded-lg p-6 border border-border">
              <h3 className="font-serif text-lg font-semibold mb-4">Contact Information</h3>
              
              <ul className="space-y-4">
                {provider.contact.phone && (
                  <li className="flex items-center">
                    <Phone className="h-4 w-4 text-morocco-teal mr-3" />
                    <a href={`tel:${provider.contact.phone}`} className="hover:text-morocco-teal">
                      {provider.contact.phone}
                    </a>
                  </li>
                )}
                
                {provider.contact.email && (
                  <li className="flex items-center">
                    <Mail className="h-4 w-4 text-morocco-teal mr-3" />
                    <a href={`mailto:${provider.contact.email}`} className="hover:text-morocco-teal">
                      {provider.contact.email}
                    </a>
                  </li>
                )}
                
                {provider.contact.website && (
                  <li className="flex items-center">
                    <Globe className="h-4 w-4 text-morocco-teal mr-3" />
                    <a 
                      href={`https://${provider.contact.website}`} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="hover:text-morocco-teal"
                    >
                      {provider.contact.website}
                    </a>
                  </li>
                )}
              </ul>
              
              {provider.contact.social && (
                <>
                  <Separator className="my-4" />
                  
                  <h4 className="font-medium mb-3">Follow on Social Media</h4>
                  <div className="flex space-x-3">
                    {provider.contact.social.instagram && (
                      <a 
                        href={`https://instagram.com/${provider.contact.social.instagram.replace('@', '')}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-morocco-teal/10 text-morocco-teal rounded-full hover:bg-morocco-teal/20"
                      >
                        <Instagram className="h-5 w-5" />
                        <span className="sr-only">Instagram</span>
                      </a>
                    )}
                    
                    {provider.contact.social.facebook && (
                      <a 
                        href={`https://facebook.com/${provider.contact.social.facebook}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-morocco-teal/10 text-morocco-teal rounded-full hover:bg-morocco-teal/20"
                      >
                        <Facebook className="h-5 w-5" />
                        <span className="sr-only">Facebook</span>
                      </a>
                    )}
                  </div>
                </>
              )}
              
              <Separator className="my-4" />
              
              <Button className="w-full bg-morocco-teal hover:bg-morocco-teal/90 mb-2">
                Request a Quote
              </Button>
              <Button variant="outline" className="w-full border-morocco-teal text-morocco-teal">
                Save to Favorites
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}