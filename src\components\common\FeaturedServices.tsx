import { useState, useEffect } from 'react';
import ServiceCard from './ServiceCard';
import { Button } from '@/components/ui/button';
import { ServiceProvider } from '@/types';
import { getFeaturedProviders } from '@/data/providers';

export default function FeaturedServices() {
  const [featuredProviders, setFeaturedProviders] = useState<ServiceProvider[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadFeaturedServices = async () => {
      setLoading(true);
      // Simulate API delay for better UX demonstration
      await new Promise(resolve => setTimeout(resolve, 500));
      setFeaturedProviders(getFeaturedProviders());
      setLoading(false);
    };

    loadFeaturedServices();
  }, []);
  
  return (
    <section className="py-12 md:py-16">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="font-serif text-2xl md:text-3xl font-semibold">
            Featured Services
          </h2>
          <Button variant="outline" className="border-morocco-teal text-morocco-teal hover:bg-morocco-teal hover:text-white">
            View All
          </Button>
        </div>
        
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-muted rounded-lg h-48 mb-4"></div>
                <div className="bg-muted rounded h-4 mb-2"></div>
                <div className="bg-muted rounded h-4 w-3/4"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {featuredProviders.map((provider) => (
              <ServiceCard key={provider.id} provider={provider} />
            ))}
          </div>
        )}
      </div>
    </section>
  );
}