import { Link } from 'react-router-dom';
import { Star, MapPin } from 'lucide-react';
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { ServiceProvider } from '@/types';
import { getLocationById } from '@/data/locations';
import { useState } from 'react';

interface ServiceCardProps {
  provider: ServiceProvider;
}

export default function ServiceCard({ provider }: ServiceCardProps) {
  const location = getLocationById(provider.location);
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Link
      to={`/service/${provider.id}`}
      aria-label={`View details for ${provider.name} in ${location?.name || 'Unknown location'}`}
    >
      <Card className="overflow-hidden h-full transition-all duration-200 hover:shadow-md hover:border-morocco-teal/20 hover-lift" role="article">
        {/* Image */}
        <div className="relative h-48 w-full overflow-hidden">
          {provider.images && provider.images.length > 0 && !imageError ? (
            <img
              src={provider.images[0]}
              alt={provider.name}
              className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
              onError={handleImageError}
              loading="lazy"
            />
          ) : (
            <div className="h-full w-full bg-muted flex items-center justify-center">
              <span className="text-muted-foreground">
                {imageError ? 'Image not available' : 'No Image'}
              </span>
            </div>
          )}
          {provider.featured && (
            <div className="absolute top-2 right-2 bg-morocco-gold/90 text-white text-xs font-medium px-2 py-0.5 rounded-sm">
              Featured
            </div>
          )}
        </div>
        
        <CardContent className="p-4">
          <div className="flex justify-between items-start">
            <h3 className="font-serif text-lg font-medium">{provider.name}</h3>
            {provider.rating && (
              <div
                className="flex items-center gap-1 bg-morocco-gold/10 px-2 py-0.5 rounded"
                aria-label={`Rating: ${provider.rating} out of 5 stars`}
              >
                <Star className="h-3 w-3 fill-morocco-gold text-morocco-gold" aria-hidden="true" />
                <span className="text-sm font-medium">{provider.rating}</span>
              </div>
            )}
          </div>
          
          {/* Price range */}
          {provider.priceRange && (
            <div className="mt-1 text-sm text-muted-foreground">
              {provider.priceRange === '$' ? 'Budget' : 
               provider.priceRange === '$$' ? 'Mid-range' : 
               provider.priceRange === '$$$' ? 'Premium' : 
               provider.priceRange}
            </div>
          )}
          
          {/* Description */}
          <p className="mt-2 text-sm line-clamp-2">{provider.description}</p>
        </CardContent>
        
        <CardFooter className="px-4 py-3 border-t flex items-center text-sm text-muted-foreground">
          <MapPin className="h-3 w-3 mr-1" />
          <span>{location?.name || 'Unknown location'}</span>
        </CardFooter>
      </Card>
    </Link>
  );
}