import { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { ChevronRight, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import ServiceCard from '@/components/common/ServiceCard';
import { searchProviders } from '@/data/providers';
import { ServiceProvider } from '@/types';
import { locationsData } from '@/data/locations';
import { categoriesData } from '@/data/categories';

export default function SearchResults() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [results, setResults] = useState<ServiceProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('relevance');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const query = searchParams.get('q') || '';
  const locationParam = searchParams.get('location') || '';

  useEffect(() => {
    const performSearch = async () => {
      setLoading(true);
      
      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 300));
        
        let searchResults = searchProviders(query);
        
        // Filter by location if specified
        if (selectedLocation || locationParam) {
          const locationFilter = selectedLocation || locationParam;
          searchResults = searchResults.filter(provider => 
            provider.location === locationFilter
          );
        }
        
        // Filter by category if specified
        if (selectedCategory) {
          searchResults = searchResults.filter(provider => 
            provider.category === selectedCategory
          );
        }
        
        // Sort results
        switch (sortBy) {
          case 'rating':
            searchResults.sort((a, b) => (b.rating || 0) - (a.rating || 0));
            break;
          case 'priceAsc':
            searchResults.sort((a, b) => {
              const getPriceValue = (range?: string) => {
                if (!range) return 0;
                if (range === '$') return 1;
                if (range === '$$') return 2;
                if (range === '$$$') return 3;
                return 0;
              };
              return getPriceValue(a.priceRange) - getPriceValue(b.priceRange);
            });
            break;
          case 'priceDesc':
            searchResults.sort((a, b) => {
              const getPriceValue = (range?: string) => {
                if (!range) return 0;
                if (range === '$') return 1;
                if (range === '$$') return 2;
                if (range === '$$$') return 3;
                return 0;
              };
              return getPriceValue(b.priceRange) - getPriceValue(a.priceRange);
            });
            break;
          default:
            // Keep relevance order (default from search)
            break;
        }
        
        setResults(searchResults);
      } catch (error) {
        console.error('Search failed:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    };

    if (query) {
      performSearch();
    } else {
      setResults([]);
      setLoading(false);
    }
  }, [query, selectedLocation, selectedCategory, sortBy, locationParam]);

  const updateSearchParams = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    setSearchParams(newParams);
  };

  return (
    <div>
      {/* Breadcrumbs */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <Link to="/" className="hover:text-morocco-teal">Home</Link>
          <ChevronRight className="h-4 w-4 mx-1" />
          <span>Search Results</span>
          {query && (
            <>
              <ChevronRight className="h-4 w-4 mx-1" />
              <span>"{query}"</span>
            </>
          )}
        </div>
      </div>

      {/* Search Header */}
      <div className="bg-muted/30 py-8">
        <div className="container mx-auto px-4">
          <h1 className="font-serif text-3xl md:text-4xl font-bold mb-4">
            Search Results
          </h1>
          {query && (
            <p className="text-muted-foreground">
              {loading ? 'Searching...' : `${results.length} results found for "${query}"`}
            </p>
          )}
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span className="text-sm font-medium">Filters:</span>
          </div>
          
          <Select value={selectedLocation} onValueChange={setSelectedLocation}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Locations" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Locations</SelectItem>
              {locationsData.map((location) => (
                <SelectItem key={location.id} value={location.id}>
                  {location.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Categories</SelectItem>
              {categoriesData.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="rating">Highest Rating</SelectItem>
              <SelectItem value="priceAsc">Price: Low to High</SelectItem>
              <SelectItem value="priceDesc">Price: High to Low</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator className="mb-8" />

        {/* Results */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-morocco-teal"></div>
            <span className="ml-2">Searching...</span>
          </div>
        ) : results.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {results.map((provider) => (
              <ServiceCard key={provider.id} provider={provider} />
            ))}
          </div>
        ) : query ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium mb-2">No results found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search terms or filters
            </p>
            <Button 
              onClick={() => {
                setSelectedLocation('');
                setSelectedCategory('');
                setSortBy('relevance');
              }}
              variant="outline"
            >
              Clear Filters
            </Button>
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">Enter a search term to find services</p>
          </div>
        )}
      </div>
    </div>
  );
}
