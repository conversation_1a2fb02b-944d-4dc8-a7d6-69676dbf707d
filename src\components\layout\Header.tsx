import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, MapPin, Menu } from 'lucide-react';
import { locationsData } from '../../data/locations';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface HeaderProps {
  toggleSidebar: () => void;
}

export default function Header({ toggleSidebar }: HeaderProps) {
  const [location, setLocation] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const navigate = useNavigate();

  const handleLocationSelect = (locationId: string) => {
    setLocation(locationId);
    // Store in localStorage for persistence
    localStorage.setItem('selectedLocation', locationId);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to search results page with query parameters
      const searchParams = new URLSearchParams();
      searchParams.set('q', searchQuery.trim());
      if (location) {
        searchParams.set('location', location);
      }
      navigate(`/search?${searchParams.toString()}`);
    }
  };

  return (
    <header className="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
      <div className="flex items-center justify-between px-4 h-16">
        {/* Mobile Menu Button */}
        <Button 
          variant="ghost" 
          size="icon" 
          className="md:hidden mr-2" 
          onClick={toggleSidebar}
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle sidebar</span>
        </Button>

        {/* Logo */}
        <Link to="/" className="flex items-center">
          <h1 className="font-serif text-xl md:text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-morocco-teal to-morocco-gold">
            Moroccan Celebrations
          </h1>
        </Link>

        <div className="flex items-center gap-2 md:gap-4">
          {/* Location Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center gap-1 text-sm">
                <MapPin className="h-4 w-4 text-morocco-teal" />
                <span className="hidden md:inline">
                  {location ? 
                    locationsData.find(loc => loc.id === location)?.name || 'Select Location' 
                    : 'Select Location'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {locationsData.map((loc) => (
                <DropdownMenuItem 
                  key={loc.id}
                  onClick={() => handleLocationSelect(loc.id)}
                  className="cursor-pointer"
                >
                  {loc.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Search Bar */}
          <form 
            onSubmit={handleSearch} 
            className="relative hidden md:flex items-center"
          >
            <Input
              type="search"
              placeholder="Search services, vendors..."
              className="w-[180px] lg:w-[280px] h-9 bg-muted pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </form>

          {/* Mobile Search Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setShowMobileSearch(!showMobileSearch)}
          >
            <Search className="h-5 w-5" />
            <span className="sr-only">Search</span>
          </Button>
        </div>
      </div>

      {/* Mobile Search Overlay */}
      {showMobileSearch && (
        <div className="md:hidden bg-background border-b border-border p-4">
          <form onSubmit={handleSearch} className="relative">
            <Input
              type="search"
              placeholder="Search services, vendors..."
              className="w-full h-10 bg-muted pl-8 pr-4"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoFocus
            />
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </form>
        </div>
      )}
    </header>
  );
}