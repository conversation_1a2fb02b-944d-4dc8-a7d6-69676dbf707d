import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  ChevronDown, 
  ChevronRight, 
  Building, 
  Sparkles, 
  Camera, 
  Utensils, 
  Music, 
  Paintbrush, 
  Shirt, 
  Car, 
  Gift 
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { categoriesData } from '@/data/categories';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

// Icon mapping
const iconMap = {
  'Building': Building,
  'Sparkles': Sparkles,
  'Camera': Camera,
  'Utensils': Utensils,
  'Music': Music,
  'Paintbrush': Paintbrush,
  'Shirt': Shirt,
  'Car': Car,
  'Gift': Gift,
};

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const [openCategories, setOpenCategories] = useState<Record<string, boolean>>({});
  const [openSubCategories, setOpenSubCategories] = useState<Record<string, boolean>>({});

  const toggleCategory = (categoryId: string) => {
    setOpenCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  };

  const toggleSubCategory = (subcategoryId: string) => {
    setOpenSubCategories((prev) => ({
      ...prev,
      [subcategoryId]: !prev[subcategoryId],
    }));
  };

  // Get the icon component by name
  const getIconComponent = (iconName: string) => {
    return iconMap[iconName as keyof typeof iconMap] || Building;
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-50 h-full w-[280px] bg-background border-r border-border transform transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full",
          "md:translate-x-0 md:static md:z-auto" // Always visible on desktop, static positioning
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo and close button (Mobile only) */}
          <div className="h-16 flex items-center justify-between px-4 border-b border-border md:hidden">
            <Link to="/" className="font-serif text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-morocco-teal to-morocco-gold" onClick={onClose}>
              Moroccan Celebrations
            </Link>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              <span className="sr-only">Close sidebar</span>
            </Button>
          </div>

          {/* Categories Navigation */}
          <ScrollArea className="flex-1 py-4 px-2 custom-scrollbar">
            <nav className="space-y-1">
              <h2 className="px-4 text-sm font-medium text-muted-foreground mb-2">Categories</h2>
              
              {categoriesData.map((category) => (
                <Collapsible
                  key={category.id}
                  open={openCategories[category.id]}
                  onOpenChange={() => toggleCategory(category.id)}
                  className="w-full"
                >
                  {/* Desktop: Collapsible trigger with navigation */}
                  <CollapsibleTrigger asChild className="hidden md:flex w-full">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "w-full justify-between text-left font-normal px-4 py-2 h-10",
                        location.pathname === `/categories/${category.slug}` && "bg-muted"
                      )}
                      onClick={(e) => {
                        // Allow collapsible to work, but also navigate on click
                        setTimeout(() => {
                          navigate(`/categories/${category.slug}`);
                        }, 100);
                      }}
                    >
                      <span className="flex items-center gap-2">
                        {(() => {
                          const IconComponent = getIconComponent(category.icon);
                          return <IconComponent className="h-4 w-4 text-morocco-teal" />;
                        })()}
                        <span>{category.name}</span>
                      </span>
                      {openCategories[category.id] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>

                  {/* Mobile: Separate navigation and toggle */}
                  <div className="flex w-full md:hidden">
                    <Link
                      to={`/categories/${category.slug}`}
                      className="flex-grow py-2 pl-4"
                      onClick={onClose}
                    >
                      <span className="flex items-center">
                        {(() => {
                          const IconComponent = getIconComponent(category.icon);
                          return <IconComponent className="h-4 w-4 text-morocco-teal" />;
                        })()}
                        <span className="ml-2">{category.name}</span>
                      </span>
                    </Link>
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="px-2 h-10 w-10 flex-shrink-0"
                      >
                        {openCategories[category.id] ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </CollapsibleTrigger>
                  </div>
                  <CollapsibleContent className="pl-4">
                    {category.subcategories.map((subcategory) => (
                      subcategory.subSubCategories ? (
                        <Collapsible
                          key={subcategory.id}
                          open={openSubCategories[subcategory.id]}
                          onOpenChange={() => toggleSubCategory(subcategory.id)}
                        >
                          {/* Desktop: Collapsible trigger with navigation */}
                          <CollapsibleTrigger asChild className="hidden md:flex w-full">
                            <Button
                              variant="ghost"
                              size="sm"
                              className={cn(
                                "w-full justify-between text-left font-normal px-4 py-2 h-10",
                                location.pathname === `/categories/${category.slug}/${subcategory.slug}` && "bg-muted"
                              )}
                              onClick={(e) => {
                                // Allow collapsible to work, but also navigate on click
                                setTimeout(() => {
                                  navigate(`/categories/${category.slug}/${subcategory.slug}`);
                                }, 100);
                              }}
                            >
                              <span>{subcategory.name}</span>
                              {openSubCategories[subcategory.id] ? (
                                <ChevronDown className="h-3 w-3" />
                              ) : (
                                <ChevronRight className="h-3 w-3" />
                              )}
                            </Button>
                          </CollapsibleTrigger>

                          {/* Mobile: Separate navigation and toggle */}
                          <div className="flex w-full md:hidden">
                            <Link
                              to={`/categories/${category.slug}/${subcategory.slug}`}
                              className="flex-grow py-2 pl-4"
                              onClick={onClose}
                            >
                              <span>{subcategory.name}</span>
                            </Link>
                            <CollapsibleTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="px-2 h-10 w-10 flex-shrink-0"
                              >
                                {openSubCategories[subcategory.id] ? (
                                  <ChevronDown className="h-3 w-3" />
                                ) : (
                                  <ChevronRight className="h-3 w-3" />
                                )}
                              </Button>
                            </CollapsibleTrigger>
                          </div>
                          <CollapsibleContent className="pl-4">
                            {subcategory.subSubCategories?.map((subSubcategory) => (
                              <Link
                                key={subSubcategory.id}
                                to={`/categories/${category.slug}/${subcategory.slug}/${subSubcategory.slug}`}
                                onClick={onClose}
                              >
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={cn(
                                    "w-full justify-start text-left font-normal text-sm px-4 py-2 h-9",
                                    location.pathname === `/categories/${category.slug}/${subcategory.slug}/${subSubcategory.slug}` && "bg-muted text-morocco-teal"
                                  )}
                                >
                                  {subSubcategory.name}
                                </Button>
                              </Link>
                            ))}
                          </CollapsibleContent>
                        </Collapsible>
                      ) : (
                        <Link
                          key={subcategory.id}
                          to={`/categories/${category.slug}/${subcategory.slug}`}
                          onClick={onClose}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            className={cn(
                              "w-full justify-start text-left font-normal px-4 py-2 h-10",
                              location.pathname === `/categories/${category.slug}/${subcategory.slug}` && "bg-muted text-morocco-teal"
                            )}
                          >
                            {subcategory.name}
                          </Button>
                        </Link>
                      )
                    ))}
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </nav>
          </ScrollArea>

          {/* Footer */}
          <div className="p-4 border-t border-border">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">© 2025 Moroccan Celebrations</span>
              <Link to="/help" className="text-xs text-morocco-teal hover:underline" onClick={onClose}>
                Need Help?
              </Link>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}